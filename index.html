<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="心动告白 - 一个浪漫的告白应用">
    <meta name="theme-color" content="#ff6b9d">
    <title>心动告白 - Heart Confession</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" href="assets/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="assets/icon-192x192.png">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- GSAP Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Background particles -->
        <div id="particles-container"></div>
        
        <!-- Header -->
        <header class="header">
            <h1 class="app-title">心动告白</h1>
            <div class="progress-indicator">
                <span id="progress-text">0/6</span>
            </div>
        </header>
        
        <!-- Main content -->
        <main class="main-content">
            <!-- Heart container -->
            <div class="heart-container">
                <canvas id="heart-canvas" width="300" height="300"></canvas>
                <div class="heart-prompt" id="heart-prompt">
                    <span>点击❤️开始告白</span>
                </div>
            </div>
            
            <!-- Message display -->
            <div class="message-container">
                <div class="message-box" id="message-box">
                    <p id="message-text">准备好接受我的告白了吗？</p>
                </div>
            </div>
            
            <!-- Action buttons -->
            <div class="action-buttons">
                <button id="music-toggle" class="action-btn">
                    <span id="music-icon">🎵</span>
                    <span id="music-status">音乐</span>
                </button>
                <button id="share-btn" class="action-btn">
                    <span>📱</span>
                    <span>分享</span>
                </button>
                <button id="install-btn" class="action-btn" style="display:none;">
                    <span>📲</span>
                    <span>安装</span>
                </button>
                <button id="screenshot-btn" class="action-btn">
                    <span>📸</span>
                    <span>截图</span>
                </button>
            </div>
        </main>
        
        <!-- Settings modal -->
        <div class="modal" id="settings-modal">
            <div class="modal-content">
                <h2>自定义告白</h2>
                <div class="custom-messages">
                    <div class="message-input-group">
                        <label for="message-1">第1句：</label>
                        <input type="text" id="message-1" placeholder="友好的赞美...">
                    </div>
                    <div class="message-input-group">
                        <label for="message-2">第2句：</label>
                        <input type="text" id="message-2" placeholder="温暖的关心...">
                    </div>
                    <div class="message-input-group">
                        <label for="message-3">第3句：</label>
                        <input type="text" id="message-3" placeholder="真诚的欣赏...">
                    </div>
                    <div class="message-input-group">
                        <label for="message-4">第4句：</label>
                        <input type="text" id="message-4" placeholder="深情的表达...">
                    </div>
                    <div class="message-input-group">
                        <label for="message-5">第5句：</label>
                        <input type="text" id="message-5" placeholder="浪漫的承诺...">
                    </div>
                    <div class="message-input-group">
                        <label for="message-6">第6句：</label>
                        <input type="text" id="message-6" placeholder="深情的告白...">
                    </div>
                </div>
                <div class="modal-actions">
                    <button id="save-custom" class="btn-primary">保存</button>
                    <button id="cancel-custom" class="btn-secondary">取消</button>
                    <button id="reset-default" class="btn-secondary">恢复默认</button>
                </div>
            </div>
        </div>
        
        <!-- Settings button -->
        <button id="settings-btn" class="settings-btn" title="自定义告白">⚙️</button>
    </div>
    
    <!-- Background audio -->
    <audio id="bg-music" loop preload="auto">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mpeg">
    </audio>
    
    <!-- JavaScript -->
    <script src="js/app.js"></script>
</body>
</html>